class Result {

    public static int countSecurePasswordVariations(String c, String s) {
        final int MOD = 1000000007;
        int n = c.length();
        int count = 0;

        // Generate all non-empty subsequences and count those greater than s
        for (int mask = 1; mask < (1 << n); mask++) {
            StringBuilder subseq = new StringBuilder();
            for (int i = 0; i < n; i++) {
                if ((mask & (1 << i)) != 0) {
                    subseq.append(c.charAt(i));
                }
            }

            if (isLexicographicallyGreater(subseq.toString(), s)) {
                count++;
            }
        }

        return count % MOD;
    }



    private static boolean isLexicographicallyGreater(String a, String b) {
        int minLen = Math.min(a.length(), b.length());

        for (int i = 0; i < minLen; i++) {
            if (a.charAt(i) > b.charAt(i)) {
                return true;
            } else if (a.charAt(i) < b.charAt(i)) {
                return false;
            }
        }

        return a.length() > b.length();
    }








}

class Main {
    public static void main(String[] args) {
        // Test case 1
        String c1 = "bab";
        String s1 = "ab";
        int result1 = Result.countSecurePasswordVariations(c1, s1);
        System.out.println("c = \"" + c1 + "\", s = \"" + s1 + "\" -> " + result1 + " (Expected: 5)");

        // Test case 2
        String c2 = "aba";
        String s2 = "ab";
        int result2 = Result.countSecurePasswordVariations(c2, s2);
        System.out.println("c = \"" + c2 + "\", s = \"" + s2 + "\" -> " + result2 + " (Expected: 3)");

        // Test case 3
        String c3 = "a";
        String s3 = "b";
        int result3 = Result.countSecurePasswordVariations(c3, s3);
        System.out.println("c = \"" + c3 + "\", s = \"" + s3 + "\" -> " + result3 + " (Expected: 0)");

        // Additional test cases
        String c4 = "abc";
        String s4 = "a";
        int result4 = Result.countSecurePasswordVariations(c4, s4);
        System.out.println("c = \"" + c4 + "\", s = \"" + s4 + "\" -> " + result4 + " (Expected: 6)");

        String c5 = "aaa";
        String s5 = "aa";
        int result5 = Result.countSecurePasswordVariations(c5, s5);
        System.out.println("c = \"" + c5 + "\", s = \"" + s5 + "\" -> " + result5 + " (Expected: 1)");

        String c6 = "z";
        String s6 = "a";
        int result6 = Result.countSecurePasswordVariations(c6, s6);
        System.out.println("c = \"" + c6 + "\", s = \"" + s6 + "\" -> " + result6 + " (Expected: 1)");

        // Edge cases
        String c7 = "aa";
        String s7 = "a";
        int result7 = Result.countSecurePasswordVariations(c7, s7);
        System.out.println("c = \"" + c7 + "\", s = \"" + s7 + "\" -> " + result7 + " (Expected: 1)");

        String c8 = "ba";
        String s8 = "ab";
        int result8 = Result.countSecurePasswordVariations(c8, s8);
        System.out.println("c = \"" + c8 + "\", s = \"" + s8 + "\" -> " + result8 + " (Expected: 2)");

        String c9 = "abcd";
        String s9 = "abc";
        int result9 = Result.countSecurePasswordVariations(c9, s9);
        System.out.println("c = \"" + c9 + "\", s = \"" + s9 + "\" -> " + result9 + " (Expected: 12)");

        String c10 = "xyz";
        String s10 = "abc";
        int result10 = Result.countSecurePasswordVariations(c10, s10);
        System.out.println("c = \"" + c10 + "\", s = \"" + s10 + "\" -> " + result10 + " (Expected: 7)");

        // More edge cases
        String c11 = "b";
        String s11 = "a";
        int result11 = Result.countSecurePasswordVariations(c11, s11);
        System.out.println("c = \"" + c11 + "\", s = \"" + s11 + "\" -> " + result11 + " (Expected: 1)");

        String c12 = "aab";
        String s12 = "ab";
        int result12 = Result.countSecurePasswordVariations(c12, s12);
        System.out.println("c = \"" + c12 + "\", s = \"" + s12 + "\" -> " + result12 + " (Expected: 1)");

        String c13 = "abab";
        String s13 = "ab";
        int result13 = Result.countSecurePasswordVariations(c13, s13);
        System.out.println("c = \"" + c13 + "\", s = \"" + s13 + "\" -> " + result13 + " (Expected: 8)");

        System.out.println("\nAll test cases passed!");
    }
}
